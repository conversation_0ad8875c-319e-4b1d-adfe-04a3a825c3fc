from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import socketio

from BASE.http.upload import handle_upload_event

app = FastAPI(title="CodeMate Socket.IO API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# Create Socket.IO server
sio = socketio.AsyncServer(cors_allowed_origins="*")

@sio.event
async def connect(sid, _environ=None):
    """Handle client connection"""
    print(f"Client {sid} connected")

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    print(f"Client {sid} disconnected")

@sio.on("upload")
async def handle_upload(sid, data):
    """Handle upload events using the refactored upload functionality"""
    print(f"Upload event from {sid}: {data}")
    try:
        await handle_upload_event(sio, sid, data)
    except Exception as e:
        print(f"Error handling upload: {e}")
        await sio.emit("upload:error", {
            "request_id": data.get("request_id", ""),
            "status": "error",
            "message": f"Upload failed: {str(e)}"
        }, room=sid)

# Mount Socket.IO app
socket_app = socketio.ASGIApp(sio, app)

if __name__ == "__main__":
    uvicorn.run(socket_app, host="127.0.0.1", port=45214)

