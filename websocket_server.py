from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import socketio

app = FastAPI(title="CodeMate Socket.IO API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# Create Socket.IO server
sio = socketio.AsyncServer(cors_allowed_origins="*")

@sio.event
async def connect(sid, environ):
    """Handle client connection"""
    print(f"Client {sid} connected")

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    print(f"Client {sid} disconnected")

@sio.on("upload")
async def handle_upload(sid, data):
    """Handle upload events"""
    print(f"Upload event from {sid}: {data}")
    await sio.emit("upload_response", {"status": "received", "data": data}, room=sid)

# Mount Socket.IO app
socket_app = socketio.ASGIApp(sio, app)

if __name__ == "__main__":
    uvicorn.run(socket_app, host="127.0.0.1", port=45214)

