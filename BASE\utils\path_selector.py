import os
from pathlib import Path

class PathSelector:
    @staticmethod
    def get_base_path():
        """Get the base application path"""
        return Path.home() / ".codemate"

    @staticmethod
    def get_cache_path():
        """Get the cache directory path"""
        path = PathSelector.get_base_path() / "cache"
        os.makedirs(path, exist_ok=True)
        return path

    @staticmethod
    def get_logs_path():
        """Get the logs directory path"""
        path = PathSelector.get_base_path() / "logs"
        os.makedirs(path, exist_ok=True)
        return path

    @staticmethod
    def get_qdrant_db_path(make_if_not_exists: bool = True):
        path = PathSelector.get_base_path() / ".neocortex"
        if make_if_not_exists:
            os.makedirs(path, exist_ok=True)
        return path