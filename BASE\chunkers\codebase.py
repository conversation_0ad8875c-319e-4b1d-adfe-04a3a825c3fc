import os
from itertools import chain
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, Future
import traceback
from typing import Any, Callable, Coroutine
from overrides import override

from . import IChunker
from client_server.utils.chunkers.utils import (
    make_qdrant_knowledgebase_chunks_from_file,
)
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_file_stats, log_memory_usage
from client_server.utils.models.knowledgebase import (
    QdrantCodebaseMetadata,
    QdrantKnowledgeBaseChunk,
)
from client_server.utils.file_timestamp_manager import (
    get_files_needing_update,
    batch_update_timestamps,
    cleanup_stale_timestamps,
)


class CodebaseChunker(IChunker):
    def __init__(self, metadata: QdrantCodebaseMetadata):
        self.metadata = metadata

    @override
    async def process(
        self,
        progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    ) -> list[QdrantKnowledgeBaseChunk]:
        chunks = await self._make_chunks(progress_callback)
        return chunks

    async def _make_chunks(
        self,
        progress_callback: Callable[[float], Coroutine[Any, Any, Any]] | None = None,
    ) -> list[QdrantKnowledgeBaseChunk]:
        LOGGER.info("Processing Codebase type knowledge base")
        LOGGER.info(f"Found {len(self.metadata.files)} files for codebase processing")

        # Clean up stale timestamps for files that no longer exist
        LOGGER.debug("Cleaning up stale timestamp entries")
        self.metadata.file_timestamps = cleanup_stale_timestamps(
            self.metadata.file_timestamps, self.metadata.files
        )

        # Check which files need to be updated based on timestamps
        LOGGER.info("Checking files for timestamp-based updates")
        files_needing_update = get_files_needing_update(
            self.metadata.files, self.metadata.file_timestamps
        )

        if not files_needing_update:
            LOGGER.info("No files need updating based on timestamps - skipping chunking")
            return []

        LOGGER.info(f"Found {len(files_needing_update)} files needing updates out of {len(self.metadata.files)} total files")

        # Log file type distribution for files that need updating
        file_extensions = {}
        total_size = 0
        for file_path in files_needing_update:
            ext = os.path.splitext(file_path)[1] or "no_extension"
            file_extensions[ext] = file_extensions.get(ext, 0) + 1
            total_size += log_file_stats(file_path)

        LOGGER.info(f"File distribution for updates: {dict(sorted(file_extensions.items()))}")
        LOGGER.info(f"Total size of files to update: {total_size / 1024 / 1024:.2f} MB")

        try:
            chunking_phase_start = time.time()
            LOGGER.info("Starting chunk creation phase")
            log_memory_usage("before_chunking")

            MAX_WORKERS = int(os.cpu_count() * 0.4)
            chunks_by_file: dict[str, list[QdrantKnowledgeBaseChunk]] = {}
            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                chunk_futures: dict[str, Future[list[QdrantKnowledgeBaseChunk]]] = {}
                # Dispatch futures only for files that need updating
                dispatch_start = time.time()
                LOGGER.debug("Dispatching chunk creation tasks to thread pool")
                for file in files_needing_update:
                    chunk_futures[file] = executor.submit(
                        make_qdrant_knowledgebase_chunks_from_file, file
                    )
                dispatch_time = time.time() - dispatch_start
                LOGGER.debug(
                    f"Dispatched {len(chunk_futures)} chunk creation tasks in {dispatch_time:.2f}s"
                )

                # Collect the results and update timestamps for successfully processed files
                collection_start = time.time()
                completed_files = 0
                successfully_processed_files = []

                for i, (file, chunk_future) in enumerate(chunk_futures.items()):
                    file_start = time.time()
                    try:
                        # Wait for the future to complete
                        chunks_by_file[file] = chunk_future.result()
                        file_time = time.time() - file_start
                        completed_files += 1
                        successfully_processed_files.append(file)

                        file_chunk_count = len(chunks_by_file[file])
                        LOGGER.debug(
                            f"Completed chunking for file {completed_files}/{len(chunk_futures)}: {file} "
                            f"({file_chunk_count} chunks, took {file_time:.2f}s)"
                        )
                    except Exception as e:
                        LOGGER.error(f"Error processing file '{file}': {e}")
                        chunks_by_file[file] = []  # Empty list for failed files

                    progress = i / len(chunk_futures) * 100
                    LOGGER.info(f"Progress: {progress:.2f}%")
                    if progress_callback:
                        await progress_callback(progress)

                collection_time = time.time() - collection_start
                LOGGER.debug(
                    f"Chunk collection completed in {collection_time:.2f} seconds"
                )

            # Update timestamps for successfully processed files
            LOGGER.info("Updating timestamps for successfully processed files")
            updated_timestamps = batch_update_timestamps(successfully_processed_files)
            for file_path, timestamp in updated_timestamps.items():
                if timestamp > 0:  # Only update if we got a valid timestamp
                    self.metadata.update_file_timestamp(file_path, timestamp)
                    LOGGER.debug(f"Updated timestamp for '{file_path}': {timestamp}ms")

            # Flatten chunks from all files
            all_chunks = list(chain(*chunks_by_file.values()))
            total_chunks = len(all_chunks)
            chunking_phase_time = time.time() - chunking_phase_start

            LOGGER.info(
                f"Chunk creation completed. Total chunks created: {total_chunks} in {chunking_phase_time:.2f}s"
            )
            log_memory_usage("after_chunking")

            if total_chunks == 0:
                LOGGER.warning("No chunks found after processing files - this may be expected if no files needed updates")
                return []

            # Log chunking statistics
            files_with_chunks = sum(
                1 for chunks_list in chunks_by_file.values() if len(chunks_list) > 0
            )
            files_without_chunks = len(chunks_by_file) - files_with_chunks
            avg_chunks_per_file = (
                total_chunks / files_with_chunks if files_with_chunks > 0 else 0
            )

            LOGGER.info(
                f"Chunking stats - Files with chunks: {files_with_chunks}, "
                f"Files without chunks: {files_without_chunks}, "
                f"Avg chunks per file: {avg_chunks_per_file:.1f}"
            )

            LOGGER.info(f"Updated timestamps for {len(successfully_processed_files)} successfully processed files")

        except Exception as e:
            LOGGER.error(f"Error processing files: {e}")
            LOGGER.error(f"Error traceback: {traceback.format_exc()}")
            log_memory_usage("error_state")
            return []

        return all_chunks
