TOOLS_LIST = {"knowledgebase": {
    "type": "function",
    "function": {
        "name": "context_search",
        "description": "Search for relevant information within a specific knowledge-base using semantic search.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query to find relevant information"
                },
                "kbid": {
                    "type": "string",
                    "description": "The knowledge-base ID to search within"
                }
            },
            "required": ["query", "kbid"],
        }
    }
},
"folder": {
    "type": "function",
    "function": {
        "name": "folder_search",
        "description": "Search for relevant information within a specific folder using semantic search within a knowledge-base.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query to find relevant information"
                },
                "kbid": {
                    "type": "string",
                    "description": "The knowledge-base ID to search within"
                },
                "folder_path": {
                    "type": "string",
                    "description": "The folder to search within"
                }
            },
            "required": ["query", "kbid", "folder_path"],
        }
    }
},
"websearch": {
    "type": "function",
    "function": {
        "name": "web_search",
        "description": "Search the web for relevant information and external resources.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query to find relevant information on the web"
                }
            },
            "required": ["query"]
        }
    }
},
"swagger": {
    "type": "function",
    "function": {
        "name": "swagger_search",
        "description": "Search API documentation and endpoints using Swagger/OpenAPI specifications",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query to find relevant API endpoints"
                },
                "kbid": {
                    "type": "string",
                    "description": "The knowledge base ID containing the Swagger documentation"
                }
            },
            "required": ["query", "kbid"]
        }
    }
}}



def get(contexts: list) -> dict:
    """
    RETURN THE TOOLS LIST BASED ON THE CONTEXTS PROVIDED
    """

    tools_list = []
    for context in contexts:
        tools_list.append(TOOLS_LIST.get(context["type"]))

    # De-dup tools_list
    tools_list = list(set(tools_list))
    return tools_list