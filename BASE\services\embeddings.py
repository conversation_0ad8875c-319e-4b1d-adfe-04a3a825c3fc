import asyncio
import httpx
import traceback
import socket
import subprocess
import time
import os
from pathlib import Path
from typing import Union, Optional

import BASE.local.ollama as ollama

from BASE.utils.platform_detector import PlatformDetector
from BASE.utils.path_selector import PathSelector

from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id
import constants

class Embeddings:
    class generate:
        _server_process: Optional[subprocess.Popen] = None
        _api_base: str = constants.OLLAMA_BASE
        _started = False

        # ------------ CLOUD MODE ------------ #
        @staticmethod
        async def cloud(batch: bool, texts: Union[str, list[str]]):
            if not batch:
                if isinstance(texts, str):
                    texts = [texts]
                elif isinstance(texts, list) and len(texts) > 0:
                    texts = [texts[0]]
                else:
                    raise ValueError("Provide at least one text string.")
            elif isinstance(texts, str):
                texts = [texts]

            base_url = embeddings.get().embeddings
            error = None
            retry_count = 10

            while retry_count > 0:
                try:
                    async with httpx.AsyncClient(verify=SSL_CONTEXT) as client:
                        response = await client.post(
                            f"{base_url}/generate",
                            json={"texts": texts},
                            timeout=30.0,
                        )
                    response.raise_for_status()
                    body = response.json()
                    embeddings = [item["embedding"] for item in body]

                    return embeddings if batch else embeddings[0]

                except Exception as e:
                    retry_count -= 1
                    await asyncio.sleep(1)
                    error = e
                    print(f"[Cloud Retry] {retry_count} left. Error: {e}")

            raise error

        # ------------ LOCAL MODE ------------ #
        @staticmethod
        def _get_binary_path(ollama_base_path: str) -> str:
            binary_paths = {
                "windows": f"{ollama_base_path}/ollama.exe",
                "linux": f"{ollama_base_path}/bin/ollama",
                "darwin": f"{ollama_base_path}/ollama",
            }

            os_name = PlatformDetector.get_os_name()
            if os_name not in binary_paths:
                raise ValueError(f"Unsupported platform: {os_name}")
            return binary_paths[os_name]

        def _is_port_available(port: int = 11434) -> bool:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(("localhost", port))
                    return True
            except socket.error:
                return False

        @classmethod
        def _start_ollama(cls):
            if cls._started:
                return

            target_dir = PathSelector.get_cache_path() / "ollama"
            binary_path = cls._get_binary_path(str(target_dir))

            if not cls._is_port_available():
                print("Ollama server already running.")
                cls._started = True
                return

            log_path = PathSelector.get_logs_path() / "ollama.log"
            print(f"Starting Ollama server at {binary_path}")

            try:
                with open(log_path, "w") as log_file:
                    env = os.environ.copy()
                    env["OLLAMA_MODELS"] = str(target_dir)

                    cls._server_process = subprocess.Popen(
                        [binary_path, "serve"],
                        stdout=log_file,
                        stderr=subprocess.STDOUT,
                        text=True,
                        env=env,
                        shell=False,
                    )

                    # wait & retry check
                    for retry in range(10):
                        try:
                            ollama.ps()
                            break
                        except Exception as e:
                            print(f"Retry {retry+1}/10 to start Ollama: {e}")
                            time.sleep(1)

                print("Ollama server started successfully.")
                cls._started = True

            except Exception as e:
                print(f"Failed to start Ollama: {e}")
                raise RuntimeError(f"Ollama boot failed: {e}")

        @staticmethod
        async def local(batch: bool, texts: Union[str, list[str]]):
            Embeddings.generate._start_ollama()

            if not batch:
                if isinstance(texts, str):
                    texts = [texts]
                elif isinstance(texts, list) and len(texts) > 0:
                    texts = [texts[0]]
                else:
                    raise ValueError("Provide at least one text string.")
            elif isinstance(texts, str):
                texts = [texts]

            print("Generating embeddings locally using Ollama")

            try:
                embeddings = []
                for text in texts:
                    result = ollama.embed(model="nomic-embed-text:v1.5", input=text)
                    embeddings.append(result.embeddings[0])

                return embeddings if batch else embeddings[0]

            except Exception as e:
                print(f"Ollama local embedding failed: {e}")
                traceback.print_exc()
                raise
