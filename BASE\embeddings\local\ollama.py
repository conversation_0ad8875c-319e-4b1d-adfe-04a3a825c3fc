import ollama
import socket
import subprocess
import time
import os
from subprocess import <PERSON><PERSON>
from typing import Optional, Union

from BASE.utils.platform_detector import PlatformDetector
from BASE.utils.path_selector import PathSelector

# Global state for server management
_server_process: Optional[Popen] = None
_started: bool = False


def get_binary_path(ollama_base_path: str) -> str:
    """Get the platform-specific path to the Ollama binary."""
    binary_paths = {
        "windows": f"{ollama_base_path}/ollama.exe",
        "linux": f"{ollama_base_path}/bin/ollama",
        "darwin": f"{ollama_base_path}/ollama",
    }

    os_name = PlatformDetector.get_os_name()
    if os_name not in binary_paths:
        raise ValueError(f"Unsupported platform: {os_name}")
    return binary_paths[os_name]


def is_port_available(port: int = 11434) -> bool:
    """Check if the specified port is available."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(("localhost", port))
            return True
    except socket.error:
        return False


def start_ollama_server():
    """Start the Ollama server if not already running"""
    global _server_process, _started
    
    if _started:
        return

    target_dir = PathSelector.get_cache_path() / "ollama"
    binary_path = get_binary_path(str(target_dir))

    if not is_port_available():
        print("Ollama server already running.")
        _started = True
        return

    log_path = PathSelector.get_logs_path() / "ollama.log"
    print(f"Starting Ollama server at {binary_path}")

    try:
        with open(log_path, "w") as log_file:
            env = os.environ.copy()
            env["OLLAMA_MODELS"] = str(target_dir)

            _server_process = subprocess.Popen(
                [binary_path, "serve"],
                stdout=log_file,
                stderr=subprocess.STDOUT,
                text=True,
                env=env,
                shell=False,
            )

            # wait & retry check
            for retry in range(10):
                try:
                    ollama.ps()
                    break
                except Exception as e:
                    print(f"Retry {retry+1}/10 to start Ollama: {e}")
                    time.sleep(1)

        print("Ollama server started successfully.")
        _started = True

    except Exception as e:
        print(f"Failed to start Ollama: {e}")
        raise RuntimeError(f"Ollama boot failed: {e}")


async def generate_local_embeddings(batch: bool, texts: Union[str, list[str]]) -> Union[list[float], list[list[float]]]:
    """Generate embeddings using local Ollama server."""
    start_ollama_server()

    if not batch:
        if isinstance(texts, str):
            texts = [texts]
        elif isinstance(texts, list) and len(texts) > 0:
            texts = [texts[0]]
        else:
            raise ValueError("Provide at least one text string.")
    elif isinstance(texts, str):
        texts = [texts]

    print("Generating embeddings locally using Ollama")

    try:
        embeddings = []
        for text in texts:
            result = ollama.embed(model="nomic-embed-text:v1.5", input=text)
            embeddings.append(result.embeddings[0])

        return embeddings if batch else embeddings[0]

    except Exception as e:
        print(f"Ollama local embedding failed: {e}")
        raise
