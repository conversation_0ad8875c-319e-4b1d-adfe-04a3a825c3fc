import asyncio
import time
from typing import Any, Callable, Coroutine, Optional, Union
import socketio

from BASE.embeddings.embeddings import generate_embeddings_cloud
from BASE.chunkers.codebase import process_codebase_chunks
from BASE.chunkers.github import process_github_chunks
from BASE.chunkers.docs import process_docs_chunks
from BASE.chunkers.swagger import process_swagger_chunks
from BASE.services.knowledge_bases import create_knowledge_base
from BASE.vdb.qdrant import get_qdrant_client
from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id

@logger.catch()
async def make_chunks_functional(
    upload_data: dict,
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[dict]]:
    """Create chunks using functional chunkers based on upload type."""
    try:
        await progress_fn(1)

        kb_type = upload_data.get("type", "")
        metadata = upload_data.get("metadata", {})

        if kb_type == "Codebase":
            files = metadata.get("files", [])
            file_timestamps = metadata.get("file_timestamps", {})
            return await process_codebase_chunks(files, file_timestamps, progress_fn)
        elif kb_type == "Github":
            repo_url = metadata.get("repo_url", "")
            target_dir = metadata.get("target_dir", "/tmp/repos")
            return await process_github_chunks(repo_url, target_dir, progress_fn)
        elif kb_type == "Docs":
            urls = metadata.get("urls", [])
            return await process_docs_chunks(urls, progress_fn)
        elif kb_type == "Swagger":
            endpoints = metadata.get("endpoints", [])
            base_path = metadata.get("base_path", "/tmp/swagger")
            return await process_swagger_chunks(endpoints, base_path, progress_fn)
        else:
            raise ValueError(f"Unknown knowledge base type: {kb_type}")

    except Exception as e:
        print(f"Error chunking files: {e}")
        await error_fn(str(e))
        return None

@logger.catch()
async def fill_embeddings_functional(
    chunks: list[dict],
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[dict]]:
    """Fill embeddings using cloud embeddings service."""
    try:
        if len(chunks) == 0:
            raise ValueError("No chunks found")

        print("Starting embedding generation phase using cloud embeddings")

        await progress_fn(1)

        completed_chunks = 0

        async def process_batch(batch: list[dict]):
            nonlocal completed_chunks
            contents = [chunk["metadata"]["content"] for chunk in batch]
            embeddings = await generate_embeddings_cloud(batch=True, texts=contents)
            for chunk, embedding in zip(batch, embeddings):
                chunk["embeddings"] = embedding
                completed_chunks += 1
                await progress_fn(completed_chunks / len(chunks) * 100)

        embedding_phase_start = time.time()
        BATCH_SIZE = 10
        # use asyncio.gather to process batches in parallel
        await asyncio.gather(
            *[
                process_batch(chunks[i : i + BATCH_SIZE])
                for i in range(0, len(chunks), BATCH_SIZE)
            ]
        )
        embedding_phase_time = time.time() - embedding_phase_start
        avg_embedding_time = embedding_phase_time / len(chunks)

        print(f"Embedding generation completed. Total chunks with embeddings: {len(chunks)} "
              f"in {embedding_phase_time:.2f}s (avg {avg_embedding_time:.3f}s per embedding)")

        return chunks
    except Exception as e:
        print(f"Error filling embeddings: {e}")
        await error_fn(f"Failed to fill embeddings: {e}")
        return None





@logger.catch()
async def handle_upload_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):
    """Handle upload events using functional programming approach."""

    # Extract upload data from the request
    upload_data = data
    request_id = upload_data.get("request_id", "")
    kb_name = upload_data.get("name", "")
    kb_type = upload_data.get("type", "")
    metadata = upload_data.get("metadata", {})

    print(f"Starting knowledge base creation process for: {kb_name}")
    print(f"Processing knowledge base type: {kb_type}")

    # Simple duplicate check for codebase type
    if kb_type == "Codebase" and not metadata.get("path"):
        print("Codebase knowledge base upload requires path metadata")
        await sio.emit(
            "upload:error",
            data={
                "request_id": request_id,
                "status": "error",
                "message": "Codebase knowledge base requires path information.",
            },
            to=sid,
        )
        return

    # Define processing steps
    async def step_1_chunking():
        return await make_chunks_functional(
            upload_data,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data={
                    "request_id": request_id,
                    "status": "PROGRESS",
                    "progress": progress,
                    "message": "(1/3) Chunking files",
                },
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data={
                    "request_id": request_id,
                    "status": "error",
                    "message": message,
                },
            ),
        )

    async def step_2_embeddings(chunks):
        return await fill_embeddings_functional(
            chunks,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data={
                    "request_id": request_id,
                    "status": "PROGRESS",
                    "progress": progress,
                    "message": "(2/3) Generating embeddings",
                },
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data={
                    "request_id": request_id,
                    "status": "error",
                    "message": message,
                },
            ),
        )

    # Execute processing steps
    chunks = await step_1_chunking()
    if chunks is None:
        print("No chunks returned from chunking step")
        return

    chunks = await step_2_embeddings(chunks)
    if chunks is None:
        print("No chunks returned from embedding step")
        return

    # -----------------------------------------------------------------------------------------
    # 3. Create Knowledge Base ----------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    metadata_phase_start = time.time()
    print("Starting metadata processing phase")
    await sio.emit(
        to=sid,
        event="upload:progress",
        data={
            "request_id": request_id,
            "status": "PROGRESS",
            "progress": 0,
            "message": "(3/3) Creating knowledge base",
        },
    )

    try:
        db_creation_start = time.time()
        print("Creating knowledge base entry in local database")

        # Create simple metadata dictionary
        kb_metadata = {
            "name": kb_name,
            "type": kb_type,
            "metadata": metadata
        }

        print(f"Knowledge base metadata prepared: {list(kb_metadata.keys())}")

        # Generate unique KB ID
        import uuid
        kb_id = str(uuid.uuid4())

        # Create knowledge base entry using simple function
        create_knowledge_base(
            name=kb_name,
            kbid=kb_id,
            metadata=kb_metadata,
            is_auto_indexed=False
        )

        # Store chunks in Qdrant (simplified approach)
        qdrant_client = get_qdrant_client()
        collection_name = f"kb_{kb_id}"

        # Create collection and store chunks
        from qdrant_client.models import Distance, VectorParams
        await qdrant_client.create_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
        )

        # Store chunks with embeddings
        points = []
        for i, chunk in enumerate(chunks):
            points.append({
                "id": i,
                "vector": chunk["embeddings"],
                "payload": chunk["metadata"]
            })

        if points:
            await qdrant_client.upsert(collection_name=collection_name, points=points)

        db_creation_time = time.time() - db_creation_start
        metadata_phase_time = time.time() - metadata_phase_start

        print(f"Knowledge base created successfully with {len(chunks)} chunks "
              f"(DB creation: {db_creation_time:.2f}s, total metadata phase: {metadata_phase_time:.2f}s)")

    except Exception as e:
        print(f"Error processing metadata: {e}")
        await sio.emit(
            event="upload:error",
            data={
                "request_id": request_id,
                "status": "error",
                "message": "Failed to process metadata.",
            },
            to=sid,
        )
        return

    # -----------------------------------------------------------------------------------------
    # 6. Report success to the client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    print(f"Upload process completed successfully for knowledge base: {kb_name}")
    print(f"Processed {len(chunks)} chunks")

    # Report success to the client
    await sio.emit(
        event="upload:success",
        to=sid,
        data={
            "request_id": request_id,
            "status": "success",
            "message": "Knowledgebase prepared",
            "data": {"id": kb_id, "name": kb_name, "chunks_count": len(chunks)},
        },
    )
    print(f"Success event emitted to client for request: {request_id}")

