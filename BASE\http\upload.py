from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id

@logger.catch()
async def handle_upload_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):


    # Strict path-based duplicate detection for all knowledge base types
    if upload_data.metadata and hasattr(upload_data.metadata, 'path'):
        kb_path = upload_data.metadata.path
        existing_kb = QdrantKnowledgeBase.exists_by_path(kb_path)

        if existing_kb:

            # Strict duplicate prevention: reject ANY existing KB for the same path
            # This includes both auto-indexed and manually created knowledge bases
            kb_type_description = "auto-indexed" if existing_kb.isAutoIndexed else "manually created"
            LOGGER.warning(
                f"KB:ADD path {kb_path} already has a {kb_type_description} KB (ID: {existing_kb.id}) "
                f"(check took {existence_check_time:.3f}s)"
            )

            error_message = "A knowledge base already exists for this path. Please choose a different path or remove the existing knowledge base first."

            await sio.emit(
                "upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=error_message,
                ).model_dump(),
                to=sid,
            )
            return
    elif upload_data.type == QdrantKnowledgebaseType.Codebase:
        # For codebase type, path is required
        LOGGER.error("Codebase knowledge base upload requires path metadata")
        await sio.emit(
            "upload:error",
            data=UploadResponseData(
                request_id=upload_data.request_id,
                status="error",
                message="Codebase knowledge base requires path information.",
            ).model_dump(),
            to=sid,
        )
        return

    existence_check_time = time.time() - existence_check_start
    LOGGER.debug(
        f"Knowledge base existence check passed (took {existence_check_time:.3f}s)"
    )

    LOGGER.info(f"Starting knowledge base creation process for: {upload_data.name}")

    # Start to process files
    files = []
    LOGGER.info(f"Processing knowledge base type: {upload_data.type}")

    # -----------------------------------------------------------------------------------------
    # 2. Create Knowledge Base ----------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    embeddings_backend = EmbeddingInferenceBuilder.create()
    prepare_chunks_steps = [
        # 1. Make chunks
        lambda: _make_chunks(
            upload_data,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data=UploadProgressData(
                    request_id=upload_data.request_id,
                    status=QdrantKnowledgebaseStatus.PROGRESS,
                    progress=progress,
                    message="(1/3) Chunking files",
                ).model_dump(),
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=message,
                ).model_dump(),
            ),
        ),
        # 2. Fill embeddings
        lambda: _fill_embeddings(
            chunks,
            backend=embeddings_backend,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data=UploadProgressData(
                    request_id=upload_data.request_id,
                    status=QdrantKnowledgebaseStatus.PROGRESS,
                    progress=progress,
                    message="(2/3) Generating embeddings",
                ).model_dump(),
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=message,
                ).model_dump(),
            ),
        ),
    ]

    chunks = []
    for step in prepare_chunks_steps:
        chunks = await step()
        if chunks is None:
            LOGGER.error("No chunks returned from step")
            return

    # -----------------------------------------------------------------------------------------
    # 3. Create Knowledge Base ----------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    metadata_phase_start = time.time()
    LOGGER.info("Starting metadata processing phase")
    await sio.emit(
        to=sid,
        event="upload:progress",
        data=UploadProgressData(
            request_id=upload_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=0,
            message="(3/3) Creating knowledge base",
        ).model_dump(),
    )

    try:
        db_creation_start = time.time()
        LOGGER.info("Creating knowledge base entry in local database")
        kb_metadata_dict = upload_data.model_dump()
        del kb_metadata_dict["session"]
        del kb_metadata_dict["request_id"]
        LOGGER.debug(
            f"Knowledge base metadata prepared: {list(kb_metadata_dict.keys())}"
        )

        kb = QdrantKnowledgeBase.from_chunks(
            metadata=QdrantKnowledgeBaseMetadata(**kb_metadata_dict), chunks=chunks
        )
        db_creation_time = time.time() - db_creation_start
        metadata_phase_time = time.time() - metadata_phase_start

        LOGGER.info(
            f"Knowledge base created successfully with {len(chunks)} chunks "
            f"(DB creation: {db_creation_time:.2f}s, total metadata phase: {metadata_phase_time:.2f}s)"
        )
    except Exception as e:
        LOGGER.error(f"Error processing metadata: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            event="upload:error",
            data=UploadResponseData(
                request_id=upload_data.request_id,
                status="error",
                message="Failed to process metadata.",
            ).model_dump(),
            to=sid,
        )
        return


    # -----------------------------------------------------------------------------------------
    # 6. Report success to the client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    LOGGER.info(
        f"Upload process completed successfully for knowledge base: {upload_data.name}"
    )
    LOGGER.info(f"Processed {len(files)} files into {len(chunks)} chunks")

    # Report success to the client
    await sio.emit(
        event="upload:success",
        to=sid,
        data=UploadResponseData(
            request_id=upload_data.request_id,
            status="success",
            message="Knowledgebase prepared",
            data=kb.model_dump(),
        ).model_dump(),
    )
    LOGGER.info(
        f"Success event emitted to client for request: {upload_data.request_id}"
    )

