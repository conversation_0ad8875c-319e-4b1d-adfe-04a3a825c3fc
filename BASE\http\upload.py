from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id



async def _make_chunks(
    upload_data: UploadData,
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[QdrantKnowledgeBaseChunk]]:
    try:
        await progress_fn(1)

        chunker: IChunker | None = None
        match upload_data.type:
            case QdrantKnowledgebaseType.Codebase:
                chunker = CodebaseChunker(upload_data.metadata)
            case QdrantKnowledgebaseType.Github:
                chunker = GithubChunker(upload_data.metadata)
            case QdrantKnowledgebaseType.Docs:
                chunker = DocsChunker(upload_data.metadata)
            case QdrantKnowledgebaseType.Swagger:
                chunker = SwaggerChunker(upload_data.metadata)
            case _:
                raise ValueError(f"Unknown knowledge base type: {upload_data.type}")

        return await chunker.process(progress_fn)
    except Exception as e:
        LOGGER.error(f"Error chunking files: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await error_fn(str(e))


async def _fill_embeddings(
    chunks: list[QdrantKnowledgeBaseChunk],
    backend: IEmbeddingBackend,
    progress_fn: Callable[[float], Coroutine[Any, Any, Any]],
    error_fn: Callable[[str], Coroutine[Any, Any, Any]],
) -> Optional[list[QdrantKnowledgeBaseChunk]]:
    try:
        if len(chunks) == 0:
            raise ValueError("No chunks found")

        LOGGER.info("Starting embedding generation phase")
        LOGGER.info(f"Using backend: {backend}")

        await progress_fn(1)

        completed_chunks = 0

        async def _process_batch(batch: list[QdrantKnowledgeBaseChunk]):
            nonlocal completed_chunks
            contents = [chunk.metadata.content for chunk in batch]
            embeddings = await backend.generate_batch(contents)
            for chunk, embedding in zip(batch, embeddings):
                chunk.embeddings = embedding
                completed_chunks += 1
                await progress_fn(completed_chunks / len(chunks) * 100)

        embedding_phase_start = time.time()
        BATCH_SIZE = 10
        # use asyncio.gather to process batches in parallel
        await asyncio.gather(
            *[
                _process_batch(chunks[i : i + BATCH_SIZE])
                for i in range(0, len(chunks), BATCH_SIZE)
            ]
        )
        embedding_phase_time = time.time() - embedding_phase_start
        avg_embedding_time = embedding_phase_time / len(chunks)

        LOGGER.info(
            f"Embedding generation completed. Total chunks with embeddings: {len(chunks)} "
            f"in {embedding_phase_time:.2f}s (avg {avg_embedding_time:.3f}s per embedding)"
        )

        return chunks
    except Exception as e:
        LOGGER.error(f"Error filling embeddings: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await error_fn(f"Failed to fill embeddings: {e}")






@logger.catch()
async def handle_upload_event(sio: socketio.AsyncServer, sid: str, data: dict = {}):


    # Strict path-based duplicate detection for all knowledge base types
    if upload_data.metadata and hasattr(upload_data.metadata, 'path'):
        kb_path = upload_data.metadata.path
        existing_kb = QdrantKnowledgeBase.exists_by_path(kb_path)

        if existing_kb:

            # Strict duplicate prevention: reject ANY existing KB for the same path
            # This includes both auto-indexed and manually created knowledge bases
            kb_type_description = "auto-indexed" if existing_kb.isAutoIndexed else "manually created"
            LOGGER.warning(
                f"KB:ADD path {kb_path} already has a {kb_type_description} KB (ID: {existing_kb.id}) "
                f"(check took {existence_check_time:.3f}s)"
            )

            error_message = "A knowledge base already exists for this path. Please choose a different path or remove the existing knowledge base first."

            await sio.emit(
                "upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=error_message,
                ).model_dump(),
                to=sid,
            )
            return
    elif upload_data.type == QdrantKnowledgebaseType.Codebase:
        # For codebase type, path is required
        LOGGER.error("Codebase knowledge base upload requires path metadata")
        await sio.emit(
            "upload:error",
            data=UploadResponseData(
                request_id=upload_data.request_id,
                status="error",
                message="Codebase knowledge base requires path information.",
            ).model_dump(),
            to=sid,
        )
        return

    existence_check_time = time.time() - existence_check_start
    LOGGER.debug(
        f"Knowledge base existence check passed (took {existence_check_time:.3f}s)"
    )

    LOGGER.info(f"Starting knowledge base creation process for: {upload_data.name}")

    # Start to process files
    files = []
    LOGGER.info(f"Processing knowledge base type: {upload_data.type}")

    # -----------------------------------------------------------------------------------------
    # 2. Create Knowledge Base ----------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    embeddings_backend = EmbeddingInferenceBuilder.create()
    prepare_chunks_steps = [
        # 1. Make chunks
        lambda: _make_chunks(
            upload_data,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data=UploadProgressData(
                    request_id=upload_data.request_id,
                    status=QdrantKnowledgebaseStatus.PROGRESS,
                    progress=progress,
                    message="(1/3) Chunking files",
                ).model_dump(),
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=message,
                ).model_dump(),
            ),
        ),
        # 2. Fill embeddings
        lambda: _fill_embeddings(
            chunks,
            backend=embeddings_backend,
            progress_fn=lambda progress: sio.emit(
                to=sid,
                event="upload:progress",
                data=UploadProgressData(
                    request_id=upload_data.request_id,
                    status=QdrantKnowledgebaseStatus.PROGRESS,
                    progress=progress,
                    message="(2/3) Generating embeddings",
                ).model_dump(),
            ),
            error_fn=lambda message: sio.emit(
                to=sid,
                event="upload:error",
                data=UploadResponseData(
                    request_id=upload_data.request_id,
                    status="error",
                    message=message,
                ).model_dump(),
            ),
        ),
    ]

    chunks = []
    for step in prepare_chunks_steps:
        chunks = await step()
        if chunks is None:
            LOGGER.error("No chunks returned from step")
            return

    # -----------------------------------------------------------------------------------------
    # 3. Create Knowledge Base ----------------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    metadata_phase_start = time.time()
    LOGGER.info("Starting metadata processing phase")
    await sio.emit(
        to=sid,
        event="upload:progress",
        data=UploadProgressData(
            request_id=upload_data.request_id,
            status=QdrantKnowledgebaseStatus.PROGRESS,
            progress=0,
            message="(3/3) Creating knowledge base",
        ).model_dump(),
    )

    try:
        db_creation_start = time.time()
        LOGGER.info("Creating knowledge base entry in local database")
        kb_metadata_dict = upload_data.model_dump()
        del kb_metadata_dict["session"]
        del kb_metadata_dict["request_id"]
        LOGGER.debug(
            f"Knowledge base metadata prepared: {list(kb_metadata_dict.keys())}"
        )

        kb = QdrantKnowledgeBase.from_chunks(
            metadata=QdrantKnowledgeBaseMetadata(**kb_metadata_dict), chunks=chunks
        )
        db_creation_time = time.time() - db_creation_start
        metadata_phase_time = time.time() - metadata_phase_start

        LOGGER.info(
            f"Knowledge base created successfully with {len(chunks)} chunks "
            f"(DB creation: {db_creation_time:.2f}s, total metadata phase: {metadata_phase_time:.2f}s)"
        )
    except Exception as e:
        LOGGER.error(f"Error processing metadata: {e}")
        LOGGER.error(f"Error traceback: {traceback.format_exc()}")
        await sio.emit(
            event="upload:error",
            data=UploadResponseData(
                request_id=upload_data.request_id,
                status="error",
                message="Failed to process metadata.",
            ).model_dump(),
            to=sid,
        )
        return


    # -----------------------------------------------------------------------------------------
    # 6. Report success to the client -------------------------------------------------------
    # -----------------------------------------------------------------------------------------

    LOGGER.info(
        f"Upload process completed successfully for knowledge base: {upload_data.name}"
    )
    LOGGER.info(f"Processed {len(files)} files into {len(chunks)} chunks")

    # Report success to the client
    await sio.emit(
        event="upload:success",
        to=sid,
        data=UploadResponseData(
            request_id=upload_data.request_id,
            status="success",
            message="Knowledgebase prepared",
            data=kb.model_dump(),
        ).model_dump(),
    )
    LOGGER.info(
        f"Success event emitted to client for request: {upload_data.request_id}"
    )

